<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SteamVault Pro - Steam 宝库 - 管理页面</title>
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="icon" type="image/png" href="/static/favicon.png">
    <link rel="shortcut icon" href="/static/favicon.ico">
    <link rel="stylesheet" href="/static/css/特效样式.css">
    <script src="/static/js/安全防护.js"></script>
    <script src="/static/js/特效管理器.js"></script>
    <style>
        :root {
            --primary-bg: #656f8c;
            --sidebar-bg: #373e54;
            --card-bg: #eef1f8;
            --card-purple: #f7eeff;
            --card-blue: #edf5ff;
            --text-dark: #333;
            --text-light: #666;
            --accent-green: #4CAF50;
            --accent-purple: #8e44ad;
            --accent-blue: #3498db;
            --border-radius: 12px;
            --card-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--text-dark);
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--sidebar-bg) 0%, #2c3e50 100%);
            width: 85px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px 0;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }



        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;
            align-items: center;
            padding-top: 10px;
        }

        .sidebar-icon {
            width: 55px;
            height: 55px;
            border-radius: 18px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .sidebar-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar-icon:hover::before {
            left: 100%;
        }

        .sidebar-icon-symbol {
            font-size: 22px;
            margin-bottom: 2px;
            transition: all 0.3s ease;
        }

        .sidebar-icon-text {
            font-size: 10px;
            font-weight: 500;
            opacity: 0.9;
            transition: all 0.3s ease;
        }

        .sidebar-icon:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .sidebar-icon:hover .sidebar-icon-symbol {
            transform: scale(1.1);
        }

        .sidebar-icon.active {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-color: var(--accent-blue);
            color: white;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            transform: translateY(-1px);
        }

        .sidebar-icon.active::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 30px;
            background: linear-gradient(to bottom, var(--accent-blue), var(--accent-purple));
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        }

        .sidebar-icon.active .sidebar-icon-symbol {
            transform: scale(1.1);
        }

        .sidebar-divider {
            width: 40px;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
            margin: 15px 0;
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
        }

        .sidebar-user {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent-green), #27ae60);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-user:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .main-container {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }
        
        .header {
            display: flex;
            flex-direction: column;
            margin-bottom: 30px;
        }
        
        .page-title {
            background-color: var(--card-purple);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
        }
        
        .page-title h1 {
            font-size: 26px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
        }
        
        .page-title p {
            font-size: 15px;
            color: var(--text-light);
        }
        
        .games-container {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--card-shadow);
            flex: 1;
        }

        .container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .container-title {
            font-size: 22px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .stats-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 15px;
            background: var(--card-blue);
            border-radius: 8px;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: var(--accent-blue);
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-light);
            margin-top: 2px;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
            gap: 15px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--accent-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .games-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .game-item {
            display: flex;
            align-items: center;
            padding: 20px;
            background: #fafafa;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .game-item:hover {
            background: #f0f8ff;
            border-color: var(--accent-blue);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
        }

        .game-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .game-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .game-details {
            display: flex;
            gap: 15px;
            align-items: center;
            font-size: 14px;
            color: var(--text-light);
        }

        .game-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .game-type {
            background: var(--card-purple);
            color: var(--accent-purple);
        }

        .game-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .button-delete {
            background: #e74c3c;
            color: white;
        }

        .button-delete:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }

        .button-clear-all {
            background: #e67e22;
            color: white;
            font-size: 16px;
            padding: 10px 20px;
            margin-left: 15px;
        }

        .button-clear-all:hover {
            background: #d35400;
            transform: translateY(-1px);
        }

        .button-clear-all:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: var(--text-light);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            text-align: center;
            max-width: 400px;
        }

        .error-message {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #fab1a0;
            margin-bottom: 20px;
            display: none;
        }

        /* 通知系统样式优化 */
        #notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
            max-width: 400px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .notification {
            pointer-events: auto;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .notification::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .notification:hover::before {
            left: 100%;
        }

        .notification:hover {
            transform: translateX(-5px) scale(1.02);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-container {
                margin-left: 0;
                padding: 20px;
            }

            .game-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            /* 移动端通知样式 */
            #notification-container {
                top: 15px;
                left: 15px;
                right: 15px;
                max-width: none;
                gap: 10px;
            }

            .notification {
                font-size: 14px;
                padding: 14px 16px !important;
                border-radius: 10px !important;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                padding: 15px;
            }

            .game-grid {
                gap: 12px;
            }

            /* 小屏幕通知样式 */
            #notification-container {
                top: 10px;
                left: 10px;
                right: 10px;
                gap: 8px;
            }

            .notification {
                font-size: 13px;
                padding: 12px 14px !important;
                border-radius: 8px !important;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <a href="/" class="sidebar-icon" title="首页 - 浏览游戏列表">
                <div class="sidebar-icon-symbol">🏠</div>
                <div class="sidebar-icon-text">首页</div>
            </a>

            <a href="/管理" class="sidebar-icon active" title="管理页面 - 游戏管理">
                <div class="sidebar-icon-symbol">📊</div>
                <div class="sidebar-icon-text">管理</div>
            </a>

            <a href="/附加功能" class="sidebar-icon" title="附加功能 - 扩展工具">
                <div class="sidebar-icon-symbol">🔧</div>
                <div class="sidebar-icon-text">附加功能</div>
            </a>

            <a href="/设置" class="sidebar-icon" title="设置 - 系统配置">
                <div class="sidebar-icon-symbol">⚙️</div>
                <div class="sidebar-icon-text">设置</div>
            </a>

            <a href="https://wish.steamlab.cc/" class="sidebar-icon" title="许愿 - 游戏许愿" target="_blank">
                <div class="sidebar-icon-symbol">🌟</div>
                <div class="sidebar-icon-text">许愿</div>
            </a>

            <a href="https://help.steamlab.cc/%E4%B8%BB%E9%A1%B5.html" class="sidebar-icon" title="会员申请 - 申请会员服务" target="_blank">
                <div class="sidebar-icon-symbol">👑</div>
                <div class="sidebar-icon-text">会员申请</div>
            </a>

            <a href="https://help.steamlab.cc/%E5%94%AE%E5%90%8E%E5%B7%A5%E5%8D%95.html" class="sidebar-icon" title="售后工单 - 提交售后服务" target="_blank">
                <div class="sidebar-icon-symbol">🎫</div>
                <div class="sidebar-icon-text">售后工单</div>
            </a>
        </nav>
    </div>
    
    <div class="main-container">
        <div class="header">
            <div class="page-title">
                <h1>管理页面</h1>
                <p>您可以在这里管理游戏资源和安装包</p>
            </div>
        </div>
        
        <div class="games-container">
            <div class="container-header">
                <h2 class="container-title">已入库游戏管理</h2>
                <div class="stats-info">
                    <div class="stat-item">
                        <div class="stat-number" id="total-games">0</div>
                        <div class="stat-label">已入库游戏</div>
                    </div>
                    <button class="action-button button-clear-all" id="clear-all-btn" onclick="clearAllGames()" style="display: none;">
                        🗑️ 移除全部
                    </button>
                </div>
            </div>

            <div class="error-message" id="error-message"></div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在加载已入库游戏...</p>
            </div>

            <div class="games-list" id="games-list" style="display: none;">
                <!-- 游戏列表将在这里动态生成 -->
            </div>

            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">🎮</div>
                <div class="empty-title">暂无已入库游戏</div>
                <div class="empty-description">您还没有入库任何游戏。请前往首页选择游戏进行入库操作。</div>
            </div>
        </div>

        <script>
            // 全局变量存储游戏数据
            let gameDataCache = {};
            let isGameDataLoaded = false;

            // 页面加载完成后获取已入库游戏列表
            document.addEventListener('DOMContentLoaded', function() {
                loadInstalledGames();
            });

            // 加载已入库游戏列表（包含游戏数据获取）
            async function loadInstalledGames() {
                try {
                    showLoading(true);
                    hideError();

                    const response = await fetch('/api/installed-games?include_game_data=true');
                    const result = await response.json();

                    if (result.status === 'success') {
                        // 缓存游戏数据
                        if (result.game_data_cache) {
                            gameDataCache = result.game_data_cache;
                            isGameDataLoaded = true;
                        }

                        displayGames(result.games);
                        updateStats(result.games.length);
                    } else {
                        showError(result.message || '获取游戏列表失败');
                    }
                } catch (error) {
                    showError('网络错误，请检查连接后重试');
                    console.error('加载游戏列表失败:', error);
                } finally {
                    showLoading(false);
                }
            }

            // 仅加载已入库游戏列表（使用缓存的游戏数据）
            async function loadInstalledGamesOnly() {
                try {
                    showLoading(true);
                    hideError();

                    // 如果有缓存的游戏数据，使用缓存数据进行本地匹配
                    if (isGameDataLoaded) {
                        const response = await fetch('/api/installed-games-simple');
                        const result = await response.json();

                        if (result.status === 'success') {
                            // 使用缓存数据匹配游戏信息
                            const matchedGames = result.games.map(game => {
                                if (gameDataCache[game.appid]) {
                                    return {
                                        appid: game.appid,
                                        name: gameDataCache[game.appid].name,
                                        type: gameDataCache[game.appid].type
                                    };
                                } else {
                                    return {
                                        appid: game.appid,
                                        name: `游戏 ${game.appid}`,
                                        type: '未知'
                                    };
                                }
                            });

                            displayGames(matchedGames);
                            updateStats(matchedGames.length);
                        } else {
                            showError(result.message || '获取游戏列表失败');
                        }
                    } else {
                        // 如果没有缓存数据，重新加载完整数据
                        loadInstalledGames();
                    }
                } catch (error) {
                    showError('网络错误，请检查连接后重试');
                    console.error('加载游戏列表失败:', error);
                } finally {
                    showLoading(false);
                }
            }

            // 显示游戏列表
            function displayGames(games) {
                const gamesList = document.getElementById('games-list');
                const emptyState = document.getElementById('empty-state');

                if (games.length === 0) {
                    gamesList.style.display = 'none';
                    emptyState.style.display = 'flex';
                    return;
                }

                gamesList.innerHTML = '';
                games.forEach(game => {
                    const gameItem = createGameItem(game);
                    gamesList.appendChild(gameItem);
                });

                gamesList.style.display = 'flex';
                emptyState.style.display = 'none';
            }

            // 创建游戏项目元素
            function createGameItem(game) {
                const item = document.createElement('div');
                item.className = 'game-item';
                item.innerHTML = `
                    <div class="game-info">
                        <div class="game-name">${game.name}</div>
                        <div class="game-details">
                            <span>AppID: ${game.appid}</span>
                            <span class="game-tag game-type">${game.type}</span>
                        </div>
                    </div>
                    <div class="game-actions">
                        <button class="action-button button-delete" onclick="deleteGame('${game.appid}', '${game.name}')">
                            🗑️ 删除
                        </button>
                    </div>
                `;
                return item;
            }

            // 更新统计信息
            function updateStats(count) {
                document.getElementById('total-games').textContent = count;

                // 控制移除全部按钮的显示
                const clearAllBtn = document.getElementById('clear-all-btn');
                if (count > 0) {
                    clearAllBtn.style.display = 'block';
                } else {
                    clearAllBtn.style.display = 'none';
                }
            }

            // 显示/隐藏加载状态
            function showLoading(show) {
                document.getElementById('loading').style.display = show ? 'flex' : 'none';
            }

            // 显示错误信息
            function showError(message) {
                const errorElement = document.getElementById('error-message');
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }

            // 隐藏错误信息
            function hideError() {
                document.getElementById('error-message').style.display = 'none';
            }

            // 删除游戏
            async function deleteGame(appid, gameName) {
                try {
                    const response = await fetch('/api/delete-game', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ appid: appid })
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        showNotification(`游戏 "${gameName}" 已成功删除`, 'success');
                        // 重新加载游戏列表（不重新获取游戏数据）
                        loadInstalledGamesOnly();
                    } else {
                        showNotification(result.message || '删除失败', 'error');
                    }
                } catch (error) {
                    showNotification('网络错误，删除失败', 'error');
                    console.error('删除游戏失败:', error);
                }
            }

            // 移除全部游戏
            async function clearAllGames() {
                try {
                    // 禁用按钮防止重复点击
                    const clearAllBtn = document.getElementById('clear-all-btn');
                    clearAllBtn.disabled = true;
                    clearAllBtn.textContent = '🔄 移除中...';

                    const response = await fetch('/api/clear-all-games', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        showNotification(`成功移除 ${result.deleted_count} 个游戏`, 'success');
                        // 重新加载游戏列表
                        loadInstalledGamesOnly();
                    } else {
                        showNotification(result.message || '移除失败', 'error');
                    }
                } catch (error) {
                    showNotification('网络错误，移除失败', 'error');
                    console.error('移除全部游戏失败:', error);
                } finally {
                    // 恢复按钮状态
                    const clearAllBtn = document.getElementById('clear-all-btn');
                    clearAllBtn.disabled = false;
                    clearAllBtn.textContent = '🗑️ 移除全部';
                }
            }

            // 通知系统管理器
            const NotificationManager = {
                container: null,
                notifications: [],

                // 初始化通知容器
                init() {
                    if (!this.container) {
                        this.container = document.createElement('div');
                        this.container.id = 'notification-container';
                        this.container.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            z-index: 10000;
                            pointer-events: none;
                            max-width: 400px;
                            display: flex;
                            flex-direction: column;
                            gap: 10px;
                        `;
                        document.body.appendChild(this.container);
                    }
                },

                // 添加通知
                add(message, type = 'info') {
                    this.init();

                    const notification = document.createElement('div');
                    notification.className = `notification notification-${type}`;

                    // 设置基础样式
                    notification.style.cssText = `
                        padding: 15px 20px;
                        border-radius: 12px;
                        color: white;
                        font-weight: 500;
                        font-size: 14px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.1);
                        pointer-events: auto;
                        cursor: pointer;
                        transform: translateX(100%) scale(0.9);
                        opacity: 0;
                        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                        max-width: 100%;
                        word-wrap: break-word;
                        position: relative;
                        overflow: hidden;
                    `;

                    // 根据类型设置背景色和图标
                    let icon = '';
                    switch(type) {
                        case 'success':
                            notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                            icon = '✓';
                            break;
                        case 'error':
                            notification.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                            icon = '✕';
                            break;
                        case 'warning':
                            notification.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
                            icon = '⚠';
                            break;
                        default:
                            notification.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
                            icon = 'ℹ';
                    }

                    // 设置内容
                    notification.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="font-size: 16px; flex-shrink: 0;">${icon}</span>
                            <span style="flex: 1;">${message}</span>
                            <span style="font-size: 12px; opacity: 0.7; cursor: pointer; padding: 2px 6px; border-radius: 4px; background: rgba(255,255,255,0.2);" onclick="NotificationManager.remove(this.closest('.notification'))">×</span>
                        </div>
                    `;

                    // 添加到容器
                    this.container.appendChild(notification);
                    this.notifications.push(notification);

                    // 进入动画
                    requestAnimationFrame(() => {
                        notification.style.transform = 'translateX(0) scale(1)';
                        notification.style.opacity = '1';
                    });

                    // 自动移除
                    setTimeout(() => {
                        this.remove(notification);
                    }, 4000);

                    // 点击移除
                    notification.addEventListener('click', () => {
                        this.remove(notification);
                    });

                    return notification;
                },

                // 移除通知
                remove(notification) {
                    if (!notification || !notification.parentNode) return;

                    // 退出动画
                    notification.style.transform = 'translateX(100%) scale(0.9)';
                    notification.style.opacity = '0';
                    notification.style.maxHeight = notification.offsetHeight + 'px';

                    setTimeout(() => {
                        notification.style.maxHeight = '0';
                        notification.style.padding = '0 20px';
                        notification.style.margin = '0';
                    }, 200);

                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                            const index = this.notifications.indexOf(notification);
                            if (index > -1) {
                                this.notifications.splice(index, 1);
                            }
                        }
                    }, 400);
                }
            };

            // 通知函数（保持兼容性）
            function showNotification(message, type = 'info') {
                return NotificationManager.add(message, type);
            }
        </script>
    </div>
</body>
</html> 